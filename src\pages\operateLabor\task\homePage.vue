<template>
  <div class="homePage" style="background: #f8f8f8">
    <Swipe :autoplay="3000" lazy-render>
      <SwipeItem v-for="(image, index) in images" :key="index">
        <img :src="image.imgUrl" />
      </SwipeItem>
    </Swipe>
    <div
      style="
        display: flex;
        flex-wrap: wrap;
        max-height: 180px
        overflow: hidden;
        background: #FFFFFF;
      "
    >
      <div
        style="
          flex: 0 0 20%;
          height: 75px;
          box-sizing: border-box;
          text-align: center;
          position: relative;
        "
        v-for="(item, index) in tagOptions"
        :key="index"
      >
        <div @click="handleSelected(item.tagId)">
          <img
            :src="item.iconUrl"
            style="
              width: 22px;
              position: absolute;
              bottom: 42px;
              right: 50%;
              margin-right: -11px;
            "
            alt=""
          />
          <div class="tags-name">{{ item.description }}</div>
        </div>
      </div>
      <div style="width: 75px; position: relative" @click="handleFilter">
        <img class="img-style" src="../../../assets/images/more2.png" alt="" />
        <div class="tags-name">更多111</div>
      </div>
    </div>
    <div style="padding: 15px 15px 0 15px; background: #fff">
      <div style="font-size: 18px; font-weight: 600">推荐职位</div>
      <div style="display: flex; justify-content: space-between">
        <div class="type-bar">
          <span
            :class="{ active: currType === 'recommend' }"
            @click="handleChangeType('recommend')"
            >推荐给我</span
          >
          <span
            :class="{ active: currType === 'assign' }"
            @click="handleChangeType('assign')"
            >指派给我</span
          >
        </div>
        <div
          class="search-area"
          style="display: flex; align-items: center; margin-top: 15px"
        >
          <div class="search-con" @click="handleSearch">
            <img
              src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/search-img.png"
            />
            <span>搜索</span>
          </div>
          <div class="search-con" @click="handleFilter">
            <img
              src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/filtrate-img.png"
            />
            <span>筛选</span>
          </div>
        </div>
      </div>
    </div>
    <div style="padding: 20px 15px 10px 15px">
      <div v-if="currType === 'recommend'">
        <div v-if="recommendList.length">
          <div class="task" v-for="(item, index) in recommendList" :key="index">
            <div
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              <div style="display: flex; flex-direction: column">
                <span
                  style="
                    width: 200px;
                    word-break: break-all;
                    word-wrap: break-word;
                    white-space: pre-wrap;
                    font-size: 15px;
                    font-weight: 600;
                    color: #262935;
                    margin-top: 7px;
                  "
                  >营销服务7</span
                >
                <span
                  style="
                    width: 200px;
                    word-break: break-all;
                    word-wrap: break-word;
                    white-space: pre-wrap;
                    font-size: 13px;
                    color: #71788f;
                    margin-top: 10px;
                  "
                  >三角洲行动有限公司</span
                >
                <div style="margin-top: 10px">
                  <!-- <img width="13px" src="" alt="" /> -->
                  <span style="font-size: 13px; color: #262935"
                    >POS营销及推广</span
                  >
                </div>
              </div>
              <div style="font-size: 20px; font-weight: 600; color: #0082ff">
                面试
              </div>
            </div>
            <div
              style="height: 1px; background: #f0f2f7; margin-top: 20px"
            ></div>
            <div
              style="
                margin-top: 10px;
                display: flex;
                justify-content: space-between;
              "
            >
              <span style="font-size: 13px; color: #71788f; margin-top: 8px"
                >2025.7.31 - 2025.12.31</span
              >
              <div class="claim-btn">立即认领</div>
            </div>
          </div>
        </div>
        <div class="no-content" v-else>
          <img
            style="width: 40%"
            src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/no-data.png"
          />
          <div style="text-align: center; margin-top: 10px">暂无任务</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Swipe, SwipeItem, Button } from 'vant'
import Tabs from 'kit/components/marketing/admin/tabs.vue'

export default {
  components: {
    Swipe,
    SwipeItem,
    Button,
    Tabs
  },
  data() {
    return {
      images: [
        {
          key: 1,
          imgUrl:
            'https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/banner1.png'
        },
        {
          key: 2,
          imgUrl:
            'https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/banner2.png'
        },
        {
          key: 3,
          imgUrl:
            'https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/banner3.png'
        }
      ],
      tagOptions: [
        {
          tagId: 43,
          description: '市场推广',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/aa3b854a28e64f48b83cf2f24eb1a3cd/市场推广.png'
        },
        {
          tagId: 44,
          description: '信息技术服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/e5040383d7314dce8ea4ac235ca37b4a/技术软件设计.png'
        },
        {
          tagId: 45,
          description: '跑腿',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/cd053d69226842d6a9ce7bad5cc1ff74/跑腿.png'
        },
        {
          tagId: 46,
          description: '设计服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/7eb1fa0926c14790875211e94d1b0036/任务标签通用符.png'
        },
        {
          tagId: 47,
          description: '信息服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/a9c501c707ce4a129b365574bafbd467/知识分享.png'
        },
        {
          tagId: 48,
          description: '装卸搬运',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/61e95bd15f2b47aab5f11836d25debbb/养护维修.png'
        },
        {
          tagId: 49,
          description: '网络营销',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/67e1aa48cf2c49cebf1dcb2b11a5a036/a.png'
        },
        {
          tagId: 50,
          description: '美容化妆',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/7ae734f600714df2afa50449e7e29954/美容化妆.png'
        },
        {
          tagId: 51,
          description: '生产加工',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/f0754d4570e045499faa6d83e691360c/生产加工.png'
        }
      ],
      options: [
        {
          label: '推荐给我',
          value: 'recommend'
        },
        {
          label: '指派给我',
          value: 'assign'
        }
      ],
      recommendList: [
        {
          id: 1,
          name: '营销服务7',
          company: '三角洲行动有限公司',
          position: 'POS营销及推广',
          time: '2025.7.31 - 2025.12.31'
        },
        {
          id: 1,
          name: '营销服务7',
          company: '三角洲行动有限公司',
          position: 'POS营销及推广',
          time: '2025.7.31 - 2025.12.31'
        },
        {
          id: 1,
          name: '营销服务7',
          company: '三角洲行动有限公司',
          position: 'POS营销及推广',
          time: '2025.7.31 - 2025.12.31'
        }
      ],
      currType: 'recommend'
    }
  },
  created() {
    // this.fetchList()
  },
  methods: {
    async fetchList() {
      const [err, r] = await client.api()
    },
    handleChangeType(type) {
      this.currType = type
      this.fetchList()
    },
    handleSearch() {
      this.$router.push('/home/<USER>')
    },
    handleFilter() {
      this.$router.push('/home/<USER>')
    }
  }
}
</script>
<style scoped>
.homePage {
  min-height: 100vh;
}
.img-style {
  width: 22px;
  position: absolute;
  bottom: 42px;
  right: 50%;
  margin-right: -11px;
}
.tags-name {
  width: 70px;
  height: 35px;
  font-size: 12px;
  color: #262935;
  text-align: center;
  position: absolute;
  bottom: 0px;
  right: 50%;
  margin-right: -35px;
}
.search-area img {
  width: 18px;
  margin-right: 4px;
}
.search-con {
  display: flex;
  font-size: 13px;
  font-weight: 400;
  color: rgba(38, 41, 53, 1);
  margin-right: 15px;
}
.type-bar {
  font-size: 15px;
  margin-top: 20px;
  height: 26px;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  font-weight: 400;
  padding: 6px 0;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
  font-weight: 500;
  padding: 6px 0;
}
.task {
  padding: 15px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 4px;
}
.claim-btn {
  width: 90px;
  height: 30px;
  background: #0082ff;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 400;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
}
</style>
