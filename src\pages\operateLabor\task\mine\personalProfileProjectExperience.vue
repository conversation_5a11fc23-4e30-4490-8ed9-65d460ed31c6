<template>
  <div
    class="projectExperience"
    style="min-height: 100vh; overflow: auto; padding: 20px 10px"
  >
    <Icon name="add" @click="addExperience" />
    <div v-for="(item, index) in experienceList" :key="index">
      <Form ref="form" @submit="handleSave">
        <div
          style="
            background-color: #f2f2f2;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div>
            <span style="color: #4983ee">|</span>
            <span style="color: #7f7f7f">项目经历</span>
          </div>
          <div>
            <Button
              class="btn"
              style="background: #277dfe"
              v-if="!item.id"
              native-type="submit"
              >保存</Button
            >
            <Button class="btn" v-if="item.id">已保存</Button>
            <Button class="btn" style="background: #fefefe; color: #7f7f7f"
              >删除</Button
            >
          </div>
        </div>
        <Field
          v-model="item.projectName"
          label="项目名称"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入项目名称' }]"
        />
        <Field
          v-model="item.post"
          label="担任角色"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入担任角色' }]"
        />
        <CellGroup>
          <Field
            v-model="item.projectStart"
            label="项目时间"
            readonly
            clickable
            :rules="[{ required: true, message: '请选择项目时间' }]"
          >
            <template #input>
              <div style="color: #cbcbce; display: flex">
                <div @click="showStartPicker = true">
                  {{ item.projectStart || '开始时间' }}
                </div>
                <div style="margin: 0 30px">-</div>
                <div @click="showEndPicker = true">
                  {{ item.projectEnd || '结束时间' }}
                </div>
              </div>
            </template>
          </Field>
        </CellGroup>

        <Popup v-model="showStartPicker" position="bottom">
          <DatetimePicker
            v-model="item.projectStart"
            type="date"
            title="选择开始时间"
            @confirm="onConfirmStart(item)"
            @cancel="showStartPicker = false"
          />
        </Popup>

        <Popup v-model="showEndPicker" position="bottom">
          <DatetimePicker
            v-model="item.projectEnd"
            type="date"
            title="选择结束时间"
            @confirm="onConfirmEnd(item)"
            @cancel="showEndPicker = false"
          />
        </Popup>
        <Field
          v-model="item.description"
          label="项目描述"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入项目描述' }]"
        />
        <Field
          v-model="item.performance"
          label="项目业绩"
          placeholder="请输入"
          :rules="[{ required: true, message: '请输入项目业绩' }]"
        />
      </Form>
    </div>
  </div>
</template>
<script>
import {
  Icon,
  Form,
  Field,
  CellGroup,
  Popup,
  DatetimePicker,
  Button
} from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Form,
    Field,
    CellGroup,
    Popup,
    DatetimePicker,
    Button
  },
  data() {
    return {
      experienceList: [],
      showStartPicker: false,
      showEndPicker: false
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
      this.experienceList = r.data.projectHistory
      if (!this.experienceList.length) {
        this.experienceList.push({
          key: Date.now(),
          projectName: '',
          post: '',
          projectStart: '',
          projectEnd: '',
          description: '',
          performance: ''
        })
      }
    },
    addExperience() {
      this.experienceList.push({
        key: Date.now(),
        projectName: '',
        post: '',
        projectStart: '',
        projectEnd: '',
        description: '',
        performance: ''
      })
    },
    onConfirmStart(item) {
      console.log('item===', item)
      item.projectStart = this.formatDate(item.projectStart)
      this.showStartPicker = false
    },
    onConfirmEnd(item) {
      item.projectEnd = this.formatDate(item.projectEnd)
      this.showEndPicker = false
    },
    formatDate(date) {
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
        2,
        '0'
      )}-${String(d.getDate()).padStart(2, '0')}`
    },
    async handleSave() {
      const [err, r] = await client.apiLaborEditLaborDetail({
        body: {
          laborId: this.$route.query.laborInfoId,
          basicInfo: null,
          photos: null,
          projectHistory: this.experienceList,
          laborBankCard: null
        }
      })
      if (err) return handleError(err)
      this.$toast('保存成功')
      this.init()
    }
  }
}
</script>
<style scoped>
.btn {
  height: 28px;
  box-sizing: border-box;
  color: #fff;
  border-radius: 10px;
  font-size: 14px;
  margin-right: 10px;
}
</style>
