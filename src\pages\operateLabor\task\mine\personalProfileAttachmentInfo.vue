<template>
  <div
    class="attachmentInfo"
    style="height: 100vh; padding: 16px; box-sizing: border-box"
  >
    <div>附件信息</div>
    <div style="color: #9f9f9f; font-size: 12px; margin-bottom: 10px">
      <div>可上传证件、技能证书、简历附件等。</div>
      <div>支持JPG、PNG、JPEG、PDF格式的文件，大小不超过10M</div>
    </div>
    <Uploader
      v-model="fileList"
      :after-read="afterRead"
      :max-count="1"
      :max-size="1024 * 1024 * 10"
      accept="image/*, application/pdf"
    ></Uploader>
    <Button class="btn" @click="confirm" :loading="loading">确 定</Button>
  </div>
</template>
<script>
import { Uploader, Button } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Uploader,
    Button
  },
  data() {
    return {
      fileList: [],
      fileId: '',
      loading: false,
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
    },
    async afterRead(file) {
      console.log('file===', file)
      const formData = new FormData()
      formData.append('file', file.file)

      const [err, r] = await client.uploadFile({
        body: formData
      })
      if (err) return handleError(err)
      this.fileId = r.data.fileId
      // file.status = 'uploading'
      // file.message = '上传中...'
    },
    async confirm() {
      if(!this.fileList.length) {
        this.$toast('请上传文件')
        return
      }
      this.loading = true
      const [err, r] = await client.apiLaborEditLaborDetail({
        body: {
          laborId: this.$route.query.laborInfoId,
          basicInfo: null,
          photos: [this.fileId],
          projectHistory: null,
          laborBankCard: null
        }
      })
      this.loading = false
      if (err) return handleError(err)
      this.$toast('上传成功')
      this.$router.push('/personalProfile')
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  position: fixed;
  left: 10vw;
  bottom: 20px;
  text-align: center;
  box-sizing: border-box;
  background: #0281ff;
  color: #fff;
  border-radius: 20px;
}
</style>
